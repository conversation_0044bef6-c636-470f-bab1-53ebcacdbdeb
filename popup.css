* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 350px;
    min-height: 500px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    direction: rtl;
}

.container {
    padding: 20px;
    background: white;
    margin: 0;
    border-radius: 0;
    min-height: 500px;
}

.header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.logo {
    width: 32px;
    height: 32px;
    margin-left: 10px;
}

.header h1 {
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.settings-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.settings-section h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.input-group {
    margin-bottom: 12px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.input-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.controls-section {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-start {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-start:hover:not(:disabled) {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-stop {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
}

.btn-stop:hover:not(:disabled) {
    background: linear-gradient(135deg, #c82333, #dc2626);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-icon {
    font-size: 12px;
}

.status-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px 0;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.status-value {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    padding: 2px 8px;
    background: white;
    border-radius: 12px;
    border: 1px solid #ddd;
}

.options-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.checkbox-group:last-child {
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    margin-left: 8px;
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.checkbox-group label {
    font-size: 12px;
    color: #666;
    cursor: pointer;
    user-select: none;
}

.footer {
    display: flex;
    gap: 10px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    font-size: 12px;
    padding: 8px 12px;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* حالات الحالة */
.status-active {
    color: #28a745 !important;
    background: #d4edda !important;
    border-color: #c3e6cb !important;
}

.status-stopped {
    color: #dc3545 !important;
    background: #f8d7da !important;
    border-color: #f5c6cb !important;
}

.status-paused {
    color: #ffc107 !important;
    background: #fff3cd !important;
    border-color: #ffeaa7 !important;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.btn:active {
    animation: pulse 0.2s ease;
}

/* Error notification styles */
.error-notification {
    animation: slideIn 0.3s ease;
}
