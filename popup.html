<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Images Downloader Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="icons/icon32.png" alt="Icon" class="logo">
            <h1>تحميل الصور التلقائي</h1>
        </div>
        
        <div class="settings-section">
            <h3>إعدادات الفلترة</h3>
            <div class="input-group">
                <label for="minWidth">الحد الأدنى للعرض (px):</label>
                <input type="number" id="minWidth" min="1" value="200" placeholder="200">
            </div>
            <div class="input-group">
                <label for="minHeight">الحد الأدنى للارتفاع (px):</label>
                <input type="number" id="minHeight" min="1" value="200" placeholder="200">
            </div>
        </div>

        <div class="controls-section">
            <button id="startBtn" class="btn btn-start">
                <span class="btn-icon">▶</span>
                بدء التحميل
            </button>
            <button id="stopBtn" class="btn btn-stop" disabled>
                <span class="btn-icon">⏹</span>
                إيقاف التحميل
            </button>
        </div>

        <div class="status-section">
            <div class="status-item">
                <span class="status-label">الحالة:</span>
                <span id="status" class="status-value">متوقف</span>
            </div>
            <div class="status-item">
                <span class="status-label">الصور المحملة:</span>
                <span id="downloadCount" class="status-value">0</span>
            </div>
            <div class="status-item">
                <span class="status-label">الصور المكتشفة:</span>
                <span id="foundCount" class="status-value">0</span>
            </div>
        </div>

        <div class="options-section">
            <div class="checkbox-group">
                <input type="checkbox" id="autoScroll" checked>
                <label for="autoScroll">مراقبة التمرير التلقائي</label>
            </div>
            <div class="checkbox-group">
                <input type="checkbox" id="skipDuplicates" checked>
                <label for="skipDuplicates">تجاهل الصور المكررة</label>
            </div>
        </div>

        <div class="footer">
            <button id="clearBtn" class="btn btn-secondary">مسح الإحصائيات</button>
            <button id="settingsBtn" class="btn btn-secondary">الإعدادات المتقدمة</button>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
