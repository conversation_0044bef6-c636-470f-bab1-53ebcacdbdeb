class ImageDownloaderContent {
    constructor() {
        this.isActive = false;
        this.downloadCount = 0;
        this.foundCount = 0;
        this.downloadedUrls = new Set();
        this.processedImages = new Set();
        this.settings = {
            minWidth: 200,
            minHeight: 200,
            autoScroll: true,
            skipDuplicates: true
        };
        
        this.scrollObserver = null;
        this.mutationObserver = null;
        this.imageObserver = null;
        
        this.initializeMessageListener();
        this.setupImageObserver();
    }

    initializeMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // للاستجابة غير المتزامنة
        });
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'startDownloading':
                this.settings = { ...this.settings, ...message.settings };
                this.startDownloading();
                sendResponse({ success: true });
                break;
                
            case 'stopDownloading':
                this.stopDownloading();
                sendResponse({ success: true });
                break;
                
            case 'getStatus':
                sendResponse({
                    isActive: this.isActive,
                    downloadCount: this.downloadCount,
                    foundCount: this.foundCount
                });
                break;
                
            case 'clearStats':
                this.clearStats();
                sendResponse({ success: true });
                break;
        }
    }

    startDownloading() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.notifyStatusChange();
        
        // البحث عن الصور الموجودة حالياً
        this.scanForImages();
        
        // إعداد مراقبة التمرير والتغييرات
        if (this.settings.autoScroll) {
            this.setupScrollObserver();
            this.setupMutationObserver();
        }
        
        console.log('Image downloader started');
    }

    stopDownloading() {
        if (!this.isActive) return;
        
        this.isActive = false;
        this.notifyStatusChange();
        
        // إيقاف جميع المراقبين
        this.disconnectObservers();
        
        console.log('Image downloader stopped');
    }

    clearStats() {
        this.downloadCount = 0;
        this.foundCount = 0;
        this.downloadedUrls.clear();
        this.processedImages.clear();
        this.notifyStatsUpdate();
    }

    setupImageObserver() {
        // مراقب لتحميل الصور الجديدة
        this.imageObserver = new IntersectionObserver((entries) => {
            if (!this.isActive) return;
            
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.processImage(img);
                }
            });
        }, {
            rootMargin: '50px'
        });
    }

    setupScrollObserver() {
        let scrollTimeout;
        let lastScrollTop = 0;
        let scrollDirection = 'down';

        const handleScroll = () => {
            if (!this.isActive) return;

            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            scrollDirection = currentScrollTop > lastScrollTop ? 'down' : 'up';
            lastScrollTop = currentScrollTop;

            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.scanForImages();
                this.checkForLazyLoadedImages();
            }, 300);
        };

        // مراقبة التمرير العادي
        window.addEventListener('scroll', handleScroll, { passive: true });

        // مراقبة التمرير بالماوس
        window.addEventListener('wheel', handleScroll, { passive: true });

        // مراقبة التمرير باللمس
        window.addEventListener('touchmove', handleScroll, { passive: true });

        // حفظ المرجع لإزالته لاحقاً
        this.scrollHandler = handleScroll;

        // فحص دوري للصور الجديدة
        this.scrollInterval = setInterval(() => {
            if (this.isActive) {
                this.scanForImages();
            }
        }, 2000);
    }

    setupMutationObserver() {
        this.mutationObserver = new MutationObserver((mutations) => {
            if (!this.isActive) return;
            
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // البحث عن الصور في العقدة الجديدة
                        const images = node.tagName === 'IMG' ? [node] : node.querySelectorAll('img');
                        images.forEach(img => this.observeImage(img));
                        
                        // البحث عن الصور في خلفيات CSS
                        this.findBackgroundImages(node);
                    }
                });
            });
        });
        
        this.mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    scanForImages() {
        // البحث عن جميع صور IMG
        const images = document.querySelectorAll('img');
        images.forEach(img => this.processImage(img));
        
        // البحث عن صور الخلفية
        this.findBackgroundImages(document.body);
        
        // البحث في SVG
        const svgImages = document.querySelectorAll('svg image');
        svgImages.forEach(img => this.processSVGImage(img));
    }

    observeImage(img) {
        if (this.imageObserver && !this.processedImages.has(img.src)) {
            this.imageObserver.observe(img);
        }
    }

    async processImage(img) {
        if (!img.src || this.processedImages.has(img.src)) return;
        
        this.processedImages.add(img.src);
        
        try {
            // التحقق من أبعاد الصورة
            const dimensions = await this.getImageDimensions(img);
            
            if (this.shouldDownloadImage(img.src, dimensions)) {
                await this.downloadImage(img.src, dimensions);
            }
            
            this.foundCount++;
            this.notifyStatsUpdate();
            
        } catch (error) {
            console.error('Error processing image:', error);
        }
    }

    processSVGImage(svgImg) {
        const href = svgImg.getAttribute('href') || svgImg.getAttribute('xlink:href');
        if (href && !this.processedImages.has(href)) {
            this.processedImages.add(href);
            
            // محاولة تحميل صورة SVG
            if (this.shouldDownloadImage(href, { width: 0, height: 0 })) {
                this.downloadImage(href, { width: 0, height: 0 });
            }
            
            this.foundCount++;
            this.notifyStatsUpdate();
        }
    }

    findBackgroundImages(element) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_ELEMENT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            const style = window.getComputedStyle(node);
            const backgroundImage = style.backgroundImage;
            
            if (backgroundImage && backgroundImage !== 'none') {
                const urls = this.extractUrlsFromBackground(backgroundImage);
                urls.forEach(url => {
                    if (!this.processedImages.has(url)) {
                        this.processedImages.add(url);
                        
                        if (this.shouldDownloadImage(url, { width: 0, height: 0 })) {
                            this.downloadImage(url, { width: 0, height: 0 });
                        }
                        
                        this.foundCount++;
                        this.notifyStatsUpdate();
                    }
                });
            }
        }
    }

    extractUrlsFromBackground(backgroundImage) {
        const urls = [];
        const regex = /url\(['"]?([^'"]+)['"]?\)/g;
        let match;
        
        while ((match = regex.exec(backgroundImage)) !== null) {
            urls.push(match[1]);
        }
        
        return urls;
    }

    async getImageDimensions(img) {
        return new Promise((resolve) => {
            if (img.naturalWidth && img.naturalHeight) {
                resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight
                });
            } else {
                const tempImg = new Image();
                tempImg.onload = () => {
                    resolve({
                        width: tempImg.naturalWidth,
                        height: tempImg.naturalHeight
                    });
                };
                tempImg.onerror = () => {
                    resolve({ width: 0, height: 0 });
                };
                tempImg.src = img.src;
            }
        });
    }

    shouldDownloadImage(url, dimensions) {
        // تجاهل الصور المكررة إذا كان الخيار مفعل
        if (this.settings.skipDuplicates && this.downloadedUrls.has(url)) {
            return false;
        }

        // تجاهل الصور الصغيرة جداً (أيقونات، إلخ)
        if (dimensions.width < this.settings.minWidth || dimensions.height < this.settings.minHeight) {
            return false;
        }

        // تجاهل data URLs الطويلة جداً
        if (url.startsWith('data:') && url.length > 1000000) {
            return false;
        }

        // تجاهل الصور من نطاقات معينة (إعلانات، تتبع، إلخ)
        const blockedDomains = [
            'googletagmanager.com',
            'google-analytics.com',
            'facebook.com/tr',
            'doubleclick.net',
            'googlesyndication.com',
            'amazon-adsystem.com',
            'adsystem.amazon',
            'scorecardresearch.com'
        ];

        if (blockedDomains.some(domain => url.includes(domain))) {
            return false;
        }

        // تجاهل الصور بامتدادات غير مرغوب فيها
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg', '.tiff'];
        const urlWithoutParams = url.split('?')[0].toLowerCase();
        const hasValidExtension = allowedExtensions.some(ext => urlWithoutParams.endsWith(ext));

        // السماح بالصور بدون امتداد واضح إذا كانت كبيرة بما فيه الكفاية
        if (!hasValidExtension && (dimensions.width < 100 || dimensions.height < 100)) {
            return false;
        }

        // تجاهل الصور الشائعة للأيقونات والشعارات الصغيرة
        const commonIconSizes = [16, 32, 48, 64, 96, 128];
        if (commonIconSizes.includes(dimensions.width) && commonIconSizes.includes(dimensions.height)) {
            return false;
        }

        // تجاهل الصور بنسبة عرض إلى ارتفاع غريبة (مثل البانرات الطويلة جداً)
        if (dimensions.width > 0 && dimensions.height > 0) {
            const aspectRatio = dimensions.width / dimensions.height;
            if (aspectRatio > 10 || aspectRatio < 0.1) {
                return false;
            }
        }

        return true;
    }

    async downloadImage(url, dimensions) {
        try {
            // إضافة URL إلى قائمة المحملة
            this.downloadedUrls.add(url);
            
            // إرسال طلب التحميل إلى background script
            chrome.runtime.sendMessage({
                action: 'downloadImage',
                url: url,
                dimensions: dimensions,
                pageUrl: window.location.href
            });
            
            this.downloadCount++;
            this.notifyStatsUpdate();
            
        } catch (error) {
            console.error('Error downloading image:', error);
        }
    }

    checkForLazyLoadedImages() {
        // البحث عن الصور التي قد تكون محملة بشكل كسول
        const lazyImages = document.querySelectorAll('img[data-src], img[data-lazy], img[loading="lazy"]');
        lazyImages.forEach(img => {
            if (img.src && !this.processedImages.has(img.src)) {
                this.processImage(img);
            }
        });

        // البحث عن الصور في إطارات iframe
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
            try {
                if (iframe.contentDocument) {
                    const iframeImages = iframe.contentDocument.querySelectorAll('img');
                    iframeImages.forEach(img => this.processImage(img));
                }
            } catch (e) {
                // تجاهل أخطاء CORS
            }
        });

        // البحث عن الصور في Shadow DOM
        this.scanShadowDOMImages(document.body);
    }

    scanShadowDOMImages(element) {
        if (element.shadowRoot) {
            const shadowImages = element.shadowRoot.querySelectorAll('img');
            shadowImages.forEach(img => this.processImage(img));

            // البحث المتكرر في Shadow DOM المتداخل
            const shadowElements = element.shadowRoot.querySelectorAll('*');
            shadowElements.forEach(el => this.scanShadowDOMImages(el));
        }

        // البحث في العناصر الفرعية
        const children = element.children;
        for (let child of children) {
            this.scanShadowDOMImages(child);
        }
    }

    disconnectObservers() {
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }

        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }

        if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
            window.removeEventListener('wheel', this.scrollHandler);
            window.removeEventListener('touchmove', this.scrollHandler);
        }

        if (this.scrollInterval) {
            clearInterval(this.scrollInterval);
        }
    }

    notifyStatusChange() {
        chrome.runtime.sendMessage({
            action: 'statusChanged',
            isActive: this.isActive
        });
    }

    notifyStatsUpdate() {
        chrome.runtime.sendMessage({
            action: 'updateStats',
            downloadCount: this.downloadCount,
            foundCount: this.foundCount
        });
    }
}

// تشغيل content script
const imageDownloader = new ImageDownloaderContent();
