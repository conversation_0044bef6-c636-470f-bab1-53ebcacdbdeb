class ImageDownloaderPopup {
    constructor() {
        this.isActive = false;
        this.downloadCount = 0;
        this.foundCount = 0;
        this.settings = {
            minWidth: 200,
            minHeight: 200,
            autoScroll: true,
            skipDuplicates: true
        };
        
        this.initializeElements();
        this.loadSettings();
        this.bindEvents();
        this.updateUI();
        this.checkCurrentStatus();
    }

    initializeElements() {
        this.elements = {
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            status: document.getElementById('status'),
            downloadCount: document.getElementById('downloadCount'),
            foundCount: document.getElementById('foundCount'),
            minWidth: document.getElementById('minWidth'),
            minHeight: document.getElementById('minHeight'),
            autoScroll: document.getElementById('autoScroll'),
            skipDuplicates: document.getElementById('skipDuplicates'),
            clearBtn: document.getElementById('clearBtn'),
            settingsBtn: document.getElementById('settingsBtn')
        };
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['imageDownloaderSettings']);
            if (result.imageDownloaderSettings) {
                this.settings = { ...this.settings, ...result.imageDownloaderSettings };
                this.updateSettingsUI();
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    async saveSettings() {
        try {
            await chrome.storage.sync.set({
                imageDownloaderSettings: this.settings
            });
        } catch (error) {
            console.error('Error saving settings:', error);
        }
    }

    updateSettingsUI() {
        this.elements.minWidth.value = this.settings.minWidth;
        this.elements.minHeight.value = this.settings.minHeight;
        this.elements.autoScroll.checked = this.settings.autoScroll;
        this.elements.skipDuplicates.checked = this.settings.skipDuplicates;
    }

    bindEvents() {
        // أزرار التحكم
        this.elements.startBtn.addEventListener('click', () => this.startDownloading());
        this.elements.stopBtn.addEventListener('click', () => this.stopDownloading());
        this.elements.clearBtn.addEventListener('click', () => this.clearStats());

        // إعدادات الفلترة
        this.elements.minWidth.addEventListener('change', (e) => {
            this.settings.minWidth = parseInt(e.target.value) || 200;
            this.saveSettings();
        });

        this.elements.minHeight.addEventListener('change', (e) => {
            this.settings.minHeight = parseInt(e.target.value) || 200;
            this.saveSettings();
        });

        // خيارات إضافية
        this.elements.autoScroll.addEventListener('change', (e) => {
            this.settings.autoScroll = e.target.checked;
            this.saveSettings();
        });

        this.elements.skipDuplicates.addEventListener('change', (e) => {
            this.settings.skipDuplicates = e.target.checked;
            this.saveSettings();
        });

        // استقبال الرسائل من content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
    }

    async checkCurrentStatus() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, { action: 'getStatus' }, (response) => {
                    if (response) {
                        this.isActive = response.isActive;
                        this.downloadCount = response.downloadCount || 0;
                        this.foundCount = response.foundCount || 0;
                        this.updateUI();
                    }
                });
            }
        } catch (error) {
            console.error('Error checking status:', error);
        }
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'updateStats':
                this.downloadCount = message.downloadCount || 0;
                this.foundCount = message.foundCount || 0;
                this.updateUI();
                break;
            case 'statusChanged':
                this.isActive = message.isActive;
                this.updateUI();
                break;
        }
    }

    async startDownloading() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'startDownloading',
                    settings: this.settings
                });
                
                this.isActive = true;
                this.updateUI();
            }
        } catch (error) {
            console.error('Error starting download:', error);
            this.showError('خطأ في بدء التحميل');
        }
    }

    async stopDownloading() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                chrome.tabs.sendMessage(tab.id, { action: 'stopDownloading' });
                
                this.isActive = false;
                this.updateUI();
            }
        } catch (error) {
            console.error('Error stopping download:', error);
        }
    }

    clearStats() {
        this.downloadCount = 0;
        this.foundCount = 0;
        this.updateUI();
        
        // إرسال رسالة لمسح الإحصائيات في content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { action: 'clearStats' });
            }
        });
    }

    updateUI() {
        // تحديث حالة الأزرار
        this.elements.startBtn.disabled = this.isActive;
        this.elements.stopBtn.disabled = !this.isActive;

        // تحديث النص والحالة
        if (this.isActive) {
            this.elements.status.textContent = 'نشط';
            this.elements.status.className = 'status-value status-active';
        } else {
            this.elements.status.textContent = 'متوقف';
            this.elements.status.className = 'status-value status-stopped';
        }

        // تحديث الإحصائيات
        this.elements.downloadCount.textContent = this.downloadCount;
        this.elements.foundCount.textContent = this.foundCount;
    }

    showError(message) {
        // إنشاء إشعار خطأ
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(errorDiv);

        // إزالة الإشعار بعد 3 ثوان
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);

        console.error(message);
    }
}

// تشغيل الـ popup عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new ImageDownloaderPopup();
});
