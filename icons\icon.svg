<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" stroke="#5a67d8" stroke-width="2"/>
  
  <!-- Download arrow -->
  <g fill="url(#iconGradient)">
    <!-- Arrow shaft -->
    <rect x="58" y="25" width="12" height="35" rx="2"/>
    
    <!-- Arrow head -->
    <polygon points="64,70 45,50 55,50 64,59 73,50 83,50"/>
    
    <!-- Download tray -->
    <rect x="35" y="75" width="58" height="8" rx="4" fill="url(#iconGradient)"/>
    <rect x="35" y="85" width="58" height="12" rx="6" fill="url(#iconGradient)" opacity="0.8"/>
  </g>
  
  <!-- Image frames -->
  <g fill="none" stroke="url(#iconGradient)" stroke-width="2" opacity="0.7">
    <!-- Frame 1 -->
    <rect x="20" y="35" width="20" height="15" rx="2"/>
    <circle cx="25" cy="42" r="2" fill="url(#iconGradient)"/>
    <polygon points="22,47 28,42 32,45 38,40" stroke-width="1.5"/>
    
    <!-- Frame 2 -->
    <rect x="88" y="45" width="18" height="13" rx="2"/>
    <circle cx="92" cy="50" r="1.5" fill="url(#iconGradient)"/>
    <polygon points="90,55 94,51 97,53 104,48" stroke-width="1.5"/>
  </g>
  
  <!-- Sparkle effects -->
  <g fill="#ffffff" opacity="0.8">
    <circle cx="25" cy="25" r="2"/>
    <circle cx="100" cy="30" r="1.5"/>
    <circle cx="105" cy="70" r="2"/>
    <circle cx="20" cy="90" r="1.5"/>
  </g>
</svg>
