class ImageDownloaderBackground {
    constructor() {
        this.downloadQueue = [];
        this.isProcessing = false;
        this.downloadStats = {
            total: 0,
            successful: 0,
            failed: 0
        };
        
        this.initializeMessageListener();
        this.initializeDownloadListener();
    }

    initializeMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // للاستجابة غير المتزامنة
        });
    }

    initializeDownloadListener() {
        // مراقبة حالة التحميلات
        chrome.downloads.onChanged.addListener((downloadDelta) => {
            this.handleDownloadChange(downloadDelta);
        });
    }

    async handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'downloadImage':
                await this.queueImageDownload(message, sender.tab);
                sendResponse({ success: true });
                break;
                
            case 'getDownloadStats':
                sendResponse(this.downloadStats);
                break;
                
            case 'clearDownloadStats':
                this.clearStats();
                sendResponse({ success: true });
                break;
        }
    }

    async queueImageDownload(imageData, tab) {
        const downloadItem = {
            url: imageData.url,
            dimensions: imageData.dimensions,
            pageUrl: imageData.pageUrl,
            tabId: tab.id,
            timestamp: Date.now()
        };
        
        this.downloadQueue.push(downloadItem);
        
        if (!this.isProcessing) {
            this.processDownloadQueue();
        }
    }

    async processDownloadQueue() {
        if (this.isProcessing || this.downloadQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.downloadQueue.length > 0) {
            const item = this.downloadQueue.shift();
            await this.downloadImage(item);
            
            // تأخير قصير لتجنب إرهاق النظام
            await this.delay(100);
        }
        
        this.isProcessing = false;
    }

    async downloadImage(item) {
        try {
            // إنشاء اسم ملف فريد
            const filename = this.generateFilename(item);
            
            // بدء التحميل
            const downloadId = await chrome.downloads.download({
                url: item.url,
                filename: filename,
                conflictAction: 'uniquify',
                saveAs: false
            });
            
            this.downloadStats.total++;
            
            // حفظ معلومات التحميل
            await this.saveDownloadInfo(downloadId, item);
            
            console.log(`Started download: ${filename}`);
            
        } catch (error) {
            console.error('Download failed:', error);
            this.downloadStats.failed++;
            
            // إشعار المستخدم بالخطأ
            this.notifyDownloadError(item, error);
        }
    }

    generateFilename(item) {
        try {
            // استخراج اسم الملف من URL
            const url = new URL(item.url);
            let filename = url.pathname.split('/').pop();
            
            // إزالة معاملات الاستعلام
            filename = filename.split('?')[0];
            
            // التحقق من وجود امتداد
            if (!filename || !filename.includes('.')) {
                filename = `image_${Date.now()}.jpg`;
            }
            
            // إضافة معلومات الأبعاد إذا كانت متوفرة
            if (item.dimensions && item.dimensions.width && item.dimensions.height) {
                const ext = filename.split('.').pop();
                const name = filename.replace(`.${ext}`, '');
                filename = `${name}_${item.dimensions.width}x${item.dimensions.height}.${ext}`;
            }
            
            // إنشاء مجلد بناءً على النطاق
            const domain = url.hostname.replace(/^www\./, '');
            const folderName = this.sanitizeFilename(domain);
            
            return `Chrome_Images_Downloader/${folderName}/${this.sanitizeFilename(filename)}`;
            
        } catch (error) {
            console.error('Error generating filename:', error);
            return `Chrome_Images_Downloader/unknown/image_${Date.now()}.jpg`;
        }
    }

    sanitizeFilename(filename) {
        // إزالة الأحرف غير المسموحة في أسماء الملفات
        return filename.replace(/[<>:"/\\|?*]/g, '_').replace(/\s+/g, '_');
    }

    async saveDownloadInfo(downloadId, item) {
        try {
            const downloadInfo = {
                id: downloadId,
                originalUrl: item.url,
                pageUrl: item.pageUrl,
                dimensions: item.dimensions,
                timestamp: item.timestamp,
                tabId: item.tabId
            };
            
            // حفظ معلومات التحميل في storage
            const key = `download_${downloadId}`;
            await chrome.storage.local.set({ [key]: downloadInfo });
            
        } catch (error) {
            console.error('Error saving download info:', error);
        }
    }

    handleDownloadChange(downloadDelta) {
        if (downloadDelta.state) {
            switch (downloadDelta.state.current) {
                case 'complete':
                    this.downloadStats.successful++;
                    this.notifyDownloadComplete(downloadDelta.id);
                    break;
                    
                case 'interrupted':
                    this.downloadStats.failed++;
                    this.notifyDownloadFailed(downloadDelta.id);
                    break;
            }
        }
    }

    async notifyDownloadComplete(downloadId) {
        try {
            // الحصول على معلومات التحميل
            const key = `download_${downloadId}`;
            const result = await chrome.storage.local.get([key]);
            const downloadInfo = result[key];
            
            if (downloadInfo) {
                // إرسال إشعار للتاب المناسب
                chrome.tabs.sendMessage(downloadInfo.tabId, {
                    action: 'downloadComplete',
                    downloadId: downloadId,
                    url: downloadInfo.originalUrl
                }).catch(() => {
                    // تجاهل الأخطاء إذا كان التاب مغلق
                });
            }
            
        } catch (error) {
            console.error('Error notifying download complete:', error);
        }
    }

    async notifyDownloadFailed(downloadId) {
        try {
            const key = `download_${downloadId}`;
            const result = await chrome.storage.local.get([key]);
            const downloadInfo = result[key];
            
            if (downloadInfo) {
                chrome.tabs.sendMessage(downloadInfo.tabId, {
                    action: 'downloadFailed',
                    downloadId: downloadId,
                    url: downloadInfo.originalUrl
                }).catch(() => {
                    // تجاهل الأخطاء إذا كان التاب مغلق
                });
            }
            
        } catch (error) {
            console.error('Error notifying download failed:', error);
        }
    }

    notifyDownloadError(item, error) {
        chrome.tabs.sendMessage(item.tabId, {
            action: 'downloadError',
            url: item.url,
            error: error.message
        }).catch(() => {
            // تجاهل الأخطاء إذا كان التاب مغلق
        });
    }

    clearStats() {
        this.downloadStats = {
            total: 0,
            successful: 0,
            failed: 0
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تشغيل background script
const imageDownloaderBackground = new ImageDownloaderBackground();

// إعداد context menu (اختياري)
chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
        id: 'downloadImage',
        title: 'تحميل هذه الصورة',
        contexts: ['image']
    });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'downloadImage' && info.srcUrl) {
        imageDownloaderBackground.queueImageDownload({
            url: info.srcUrl,
            dimensions: { width: 0, height: 0 },
            pageUrl: info.pageUrl
        }, tab);
    }
});
