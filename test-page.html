<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة اختبار إضافة تحميل الصور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .image-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-item img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .image-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .lazy-section {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .background-image {
            width: 300px;
            height: 200px;
            margin: 10px;
            border-radius: 8px;
            display: inline-block;
        }
        
        .load-more-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .load-more-btn:hover {
            background: #5a67d8;
        }
        
        .instructions {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ صفحة اختبار إضافة تحميل الصور</h1>
        
        <div class="instructions">
            <h3>تعليمات الاختبار:</h3>
            <ol>
                <li>قم بتثبيت إضافة Chrome Images Downloader Pro</li>
                <li>انقر على أيقونة الإضافة في شريط الأدوات</li>
                <li>اضبط الحد الأدنى للأبعاد (جرب 150x150)</li>
                <li>انقر على "بدء التحميل"</li>
                <li>قم بالتمرير لأسفل لرؤية المزيد من الصور</li>
                <li>انقر على "تحميل المزيد" لإضافة صور جديدة</li>
                <li>راقب الإحصائيات في نافذة الإضافة</li>
            </ol>
        </div>

        <div class="section">
            <h2>🖼️ صور عادية (IMG tags)</h2>
            <div class="image-grid" id="normalImages">
                <div class="image-item">
                    <img src="https://picsum.photos/300/200?random=1" alt="صورة تجريبية 1">
                    <div class="image-info">300x200 - صورة عشوائية</div>
                </div>
                <div class="image-item">
                    <img src="https://picsum.photos/400/300?random=2" alt="صورة تجريبية 2">
                    <div class="image-info">400x300 - صورة عشوائية</div>
                </div>
                <div class="image-item">
                    <img src="https://picsum.photos/250/250?random=3" alt="صورة تجريبية 3">
                    <div class="image-info">250x250 - صورة مربعة</div>
                </div>
                <div class="image-item">
                    <img src="https://picsum.photos/350/200?random=4" alt="صورة تجريبية 4">
                    <div class="image-info">350x200 - صورة أفقية</div>
                </div>
            </div>
            <button class="load-more-btn" onclick="addMoreImages()">تحميل المزيد من الصور</button>
        </div>

        <div class="section">
            <h2>🎨 صور الخلفية (CSS Background)</h2>
            <div>
                <div class="background-image" style="background-image: url('https://picsum.photos/300/200?random=10'); background-size: cover; background-position: center;"></div>
                <div class="background-image" style="background-image: url('https://picsum.photos/300/200?random=11'); background-size: cover; background-position: center;"></div>
                <div class="background-image" style="background-image: url('https://picsum.photos/300/200?random=12'); background-size: cover; background-position: center;"></div>
            </div>
        </div>

        <div class="section">
            <h2>⚡ صور Lazy Loading</h2>
            <div class="lazy-section" id="lazySection">
                <p>قم بالتمرير لتحميل الصور...</p>
            </div>
            <button class="load-more-btn" onclick="loadLazyImages()">تحميل صور Lazy</button>
        </div>

        <div class="section">
            <h2>🔍 صور صغيرة (يجب تجاهلها)</h2>
            <div style="text-align: center;">
                <img src="https://picsum.photos/50/50?random=20" alt="صورة صغيرة 1" style="margin: 5px;">
                <img src="https://picsum.photos/32/32?random=21" alt="أيقونة" style="margin: 5px;">
                <img src="https://picsum.photos/64/64?random=22" alt="أيقونة كبيرة" style="margin: 5px;">
                <img src="https://picsum.photos/100/100?random=23" alt="صورة متوسطة" style="margin: 5px;">
            </div>
            <p style="color: #666; font-size: 14px;">هذه الصور صغيرة ويجب أن تتجاهلها الإضافة إذا كان الحد الأدنى أكبر من أبعادها</p>
        </div>

        <div class="section">
            <h2>📊 إحصائيات التحميل</h2>
            <div id="stats" style="background: white; padding: 15px; border-radius: 5px; font-family: monospace;">
                <div>الصور المضافة: <span id="addedCount">0</span></div>
                <div>آخر تحديث: <span id="lastUpdate">-</span></div>
            </div>
        </div>
    </div>

    <script>
        let imageCounter = 5;
        let addedCount = 0;

        function addMoreImages() {
            const container = document.getElementById('normalImages');
            
            for (let i = 0; i < 4; i++) {
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                
                const randomWidth = 200 + Math.floor(Math.random() * 200);
                const randomHeight = 150 + Math.floor(Math.random() * 150);
                
                imageItem.innerHTML = `
                    <img src="https://picsum.photos/${randomWidth}/${randomHeight}?random=${imageCounter}" alt="صورة جديدة ${imageCounter}">
                    <div class="image-info">${randomWidth}x${randomHeight} - صورة جديدة</div>
                `;
                
                container.appendChild(imageItem);
                imageCounter++;
                addedCount++;
            }
            
            updateStats();
        }

        function loadLazyImages() {
            const lazySection = document.getElementById('lazySection');
            
            for (let i = 0; i < 3; i++) {
                const img = document.createElement('img');
                img.src = `https://picsum.photos/250/150?random=${imageCounter + 100}`;
                img.alt = `صورة lazy ${imageCounter}`;
                img.style.margin = '10px';
                img.style.borderRadius = '5px';
                img.loading = 'lazy';
                
                lazySection.appendChild(img);
                imageCounter++;
                addedCount++;
            }
            
            updateStats();
        }

        function updateStats() {
            document.getElementById('addedCount').textContent = addedCount;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
        }

        // تحميل بعض الصور تلقائياً عند التمرير
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (window.scrollY > 500 && Math.random() > 0.7) {
                    // إضافة صورة عشوائية أحياناً عند التمرير
                    const container = document.getElementById('normalImages');
                    if (container.children.length < 20) {
                        const imageItem = document.createElement('div');
                        imageItem.className = 'image-item';
                        imageItem.innerHTML = `
                            <img src="https://picsum.photos/300/200?random=${imageCounter + 200}" alt="صورة تمرير ${imageCounter}">
                            <div class="image-info">300x200 - صورة تمرير</div>
                        `;
                        container.appendChild(imageItem);
                        imageCounter++;
                        addedCount++;
                        updateStats();
                    }
                }
            }, 1000);
        });

        // تحديث الإحصائيات كل ثانية
        setInterval(updateStats, 1000);
    </script>
</body>
</html>
