# Chrome Images Downloader Pro

إضافة Google Chrome احترافية لتحميل الصور تلقائياً من أي موقع ويب مع إمكانية تحديد الحد الأدنى للأبعاد ومراقبة التمرير.

## المميزات

### 🚀 المميزات الأساسية
- **تحميل تلقائي للصور**: تحميل جميع الصور في الصفحة المفتوحة تلقائياً
- **مراقبة التمرير**: اكتشاف وتحميل الصور الجديدة عند التمرير
- **فلترة الأبعاد**: تحديد الحد الأدنى للعرض والارتفاع للصور المراد تحميلها
- **تجنب التكرار**: تجاهل الصور المكررة تلقائياً
- **واجهة عربية**: واجهة مستخدم باللغة العربية مع دعم RTL

### 🔧 المميزات المتقدمة
- **اكتشاف الصور المتقدم**: 
  - صور HTML العادية (`<img>`)
  - صور الخلفية CSS (`background-image`)
  - صور SVG (`<svg><image>`)
  - صور Lazy Loading
  - صور Shadow DOM
- **فلترة ذكية**:
  - تجاهل الإعلانات وصور التتبع
  - تجاهل الأيقونات الصغيرة
  - تجاهل الصور بنسب عرض إلى ارتفاع غريبة
- **تنظيم التحميلات**: حفظ الصور في مجلدات منظمة حسب النطاق
- **إحصائيات مفصلة**: عرض عدد الصور المكتشفة والمحملة

## التثبيت

### الطريقة الأولى: تثبيت يدوي (Developer Mode)

1. **تحميل الملفات**:
   - قم بتحميل جميع ملفات الإضافة
   - تأكد من وجود جميع الملفات في مجلد واحد

2. **تفعيل وضع المطور في Chrome**:
   - افتح Chrome واذهب إلى `chrome://extensions/`
   - فعل "Developer mode" في الزاوية العلوية اليمنى
   - انقر على "Load unpacked"
   - اختر مجلد الإضافة

3. **التحقق من التثبيت**:
   - ستظهر أيقونة الإضافة في شريط الأدوات
   - انقر على الأيقونة للتأكد من عمل الواجهة

## الاستخدام

### البدء السريع

1. **افتح أي موقع ويب** يحتوي على صور
2. **انقر على أيقونة الإضافة** في شريط الأدوات
3. **اضبط الإعدادات**:
   - الحد الأدنى للعرض (افتراضي: 200px)
   - الحد الأدنى للارتفاع (افتراضي: 200px)
4. **انقر على "بدء التحميل"**
5. **قم بالتمرير** في الصفحة لتحميل المزيد من الصور
6. **انقر على "إيقاف التحميل"** عند الانتهاء

### الإعدادات المتقدمة

#### إعدادات الفلترة
- **الحد الأدنى للعرض**: أقل عرض مقبول للصور (بالبكسل)
- **الحد الأدنى للارتفاع**: أقل ارتفاع مقبول للصور (بالبكسل)

#### الخيارات الإضافية
- **مراقبة التمرير التلقائي**: تفعيل/إلغاء مراقبة التمرير
- **تجاهل الصور المكررة**: تجنب تحميل نفس الصورة أكثر من مرة

### مراقبة الإحصائيات

تعرض الإضافة إحصائيات مفيدة:
- **الحالة**: نشط/متوقف
- **الصور المحملة**: عدد الصور التي تم تحميلها بنجاح
- **الصور المكتشفة**: إجمالي عدد الصور التي تم العثور عليها

## بنية الملفات

```
Chrome Images Downloader/
├── manifest.json          # ملف التكوين الأساسي
├── popup.html             # واجهة المستخدم
├── popup.css              # تنسيقات الواجهة
├── popup.js               # منطق واجهة المستخدم
├── content.js             # سكريبت العمل على الصفحات
├── background.js          # سكريبت الخلفية
├── icons/                 # مجلد الأيقونات
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # هذا الملف
```

## الأمان والخصوصية

- **لا تجمع بيانات شخصية**: الإضافة لا تجمع أو ترسل أي بيانات شخصية
- **العمل المحلي**: جميع العمليات تتم محلياً على جهازك
- **صلاحيات محدودة**: تطلب فقط الصلاحيات الضرورية للعمل
- **مفتوحة المصدر**: يمكن مراجعة الكود بالكامل

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **الإضافة لا تعمل**:
   - تأكد من تفعيل وضع المطور
   - أعد تحميل الإضافة من `chrome://extensions/`
   - تحقق من وجود جميع الملفات

2. **لا يتم تحميل الصور**:
   - تحقق من إعدادات التحميل في Chrome
   - تأكد من أن الصور تلبي الحد الأدنى للأبعاد
   - جرب تقليل الحد الأدنى للأبعاد

3. **تحميل صور غير مرغوب فيها**:
   - زد الحد الأدنى للأبعاد
   - فعل خيار "تجاهل الصور المكررة"

## التطوير والمساهمة

### متطلبات التطوير
- معرفة بـ JavaScript و Chrome Extensions API
- فهم HTML/CSS للواجهة
- Python (لتوليد الأيقونات)

### هيكل الكود
- **popup.js**: إدارة واجهة المستخدم والإعدادات
- **content.js**: اكتشاف ومعالجة الصور في الصفحات
- **background.js**: إدارة التحميلات والتواصل بين المكونات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات للتحسين، يمكنك:
- فتح issue في المستودع
- مراجعة الكود وتقديم تحسينات
- مشاركة تجربتك مع الإضافة

---

**ملاحظة**: هذه الإضافة مصممة للاستخدام الشخصي والتعليمي. يرجى احترام حقوق الطبع والنشر عند تحميل الصور.
