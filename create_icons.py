from PIL import Image, ImageDraw
import os

def create_icon(size):
    # إنشاء صورة جديدة بخلفية شفافة
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # ألوان التدرج
    bg_color = (102, 126, 234, 255)  # اللون الأساسي
    white_color = (255, 255, 255, 255)
    
    # رسم الخلفية الدائرية
    margin = max(2, size // 20)
    circle_size = size - (margin * 2)
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                 fill=bg_color, outline=(90, 103, 216, 255), width=max(1, size // 64))
    
    # حساب الأحجام بناءً على حجم الأيقونة
    center_x, center_y = size // 2, size // 2
    
    if size >= 32:
        # رسم سهم التحميل
        arrow_width = max(2, size // 10)
        arrow_height = max(8, size // 4)
        arrow_x = center_x - arrow_width // 2
        arrow_y = center_y - arrow_height // 2 - max(2, size // 16)
        
        # جسم السهم
        draw.rectangle([arrow_x, arrow_y, arrow_x + arrow_width, arrow_y + arrow_height], 
                      fill=white_color)
        
        # رأس السهم
        arrow_head_size = max(4, size // 8)
        points = [
            (center_x, arrow_y + arrow_height + arrow_head_size),
            (center_x - arrow_head_size, arrow_y + arrow_height),
            (center_x + arrow_head_size, arrow_y + arrow_height)
        ]
        draw.polygon(points, fill=white_color)
        
        # صندوق التحميل
        box_width = max(12, size // 3)
        box_height = max(3, size // 16)
        box_x = center_x - box_width // 2
        box_y = center_y + max(8, size // 6)
        
        draw.rectangle([box_x, box_y, box_x + box_width, box_y + box_height], 
                      fill=white_color)
        draw.rectangle([box_x, box_y + box_height, box_x + box_width, box_y + box_height * 2], 
                      fill=(248, 249, 250, 200))
    
    else:
        # للأيقونات الصغيرة، رسم سهم بسيط
        arrow_size = size // 3
        points = [
            (center_x, center_y + arrow_size // 2),
            (center_x - arrow_size // 2, center_y - arrow_size // 4),
            (center_x - arrow_size // 4, center_y - arrow_size // 4),
            (center_x - arrow_size // 4, center_y - arrow_size // 2),
            (center_x + arrow_size // 4, center_y - arrow_size // 2),
            (center_x + arrow_size // 4, center_y - arrow_size // 4),
            (center_x + arrow_size // 2, center_y - arrow_size // 4)
        ]
        draw.polygon(points, fill=white_color)
    
    return img

def main():
    # إنشاء مجلد الأيقونات إذا لم يكن موجوداً
    icons_dir = "icons"
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    # أحجام الأيقونات المطلوبة
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        icon = create_icon(size)
        filename = f"{icons_dir}/icon{size}.png"
        icon.save(filename, "PNG")
        print(f"Created {filename}")
    
    print("All icons created successfully!")

if __name__ == "__main__":
    main()
