// أداة للتحقق من صحة ملفات الإضافة
const fs = require('fs');
const path = require('path');

class ExtensionValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.requiredFiles = [
            'manifest.json',
            'popup.html',
            'popup.css',
            'popup.js',
            'content.js',
            'background.js',
            'icons/icon16.png',
            'icons/icon32.png',
            'icons/icon48.png',
            'icons/icon128.png'
        ];
    }

    validate() {
        console.log('🔍 بدء التحقق من صحة الإضافة...\n');
        
        this.checkRequiredFiles();
        this.validateManifest();
        this.validateHTML();
        this.validateJavaScript();
        this.validateIcons();
        
        this.printResults();
        return this.errors.length === 0;
    }

    checkRequiredFiles() {
        console.log('📁 التحقق من الملفات المطلوبة...');
        
        this.requiredFiles.forEach(file => {
            if (!fs.existsSync(file)) {
                this.errors.push(`الملف المطلوب غير موجود: ${file}`);
            } else {
                console.log(`✅ ${file}`);
            }
        });
    }

    validateManifest() {
        console.log('\n📋 التحقق من manifest.json...');
        
        try {
            const manifestContent = fs.readFileSync('manifest.json', 'utf8');
            const manifest = JSON.parse(manifestContent);
            
            // التحقق من الحقول المطلوبة
            const requiredFields = ['manifest_version', 'name', 'version', 'permissions'];
            requiredFields.forEach(field => {
                if (!manifest[field]) {
                    this.errors.push(`حقل مطلوب غير موجود في manifest.json: ${field}`);
                } else {
                    console.log(`✅ ${field}: ${JSON.stringify(manifest[field])}`);
                }
            });

            // التحقق من إصدار manifest
            if (manifest.manifest_version !== 3) {
                this.warnings.push('يُنصح باستخدام Manifest V3');
            }

            // التحقق من الصلاحيات
            const requiredPermissions = ['activeTab', 'downloads', 'storage'];
            requiredPermissions.forEach(permission => {
                if (!manifest.permissions || !manifest.permissions.includes(permission)) {
                    this.errors.push(`صلاحية مطلوبة غير موجودة: ${permission}`);
                }
            });

        } catch (error) {
            this.errors.push(`خطأ في قراءة manifest.json: ${error.message}`);
        }
    }

    validateHTML() {
        console.log('\n🌐 التحقق من ملفات HTML...');
        
        try {
            const htmlContent = fs.readFileSync('popup.html', 'utf8');
            
            // التحقق من العناصر المطلوبة
            const requiredElements = [
                'startBtn',
                'stopBtn',
                'minWidth',
                'minHeight',
                'status',
                'downloadCount',
                'foundCount'
            ];

            requiredElements.forEach(elementId => {
                if (!htmlContent.includes(`id="${elementId}"`)) {
                    this.errors.push(`عنصر HTML مطلوب غير موجود: ${elementId}`);
                } else {
                    console.log(`✅ Element: ${elementId}`);
                }
            });

            // التحقق من ربط ملفات CSS و JS
            if (!htmlContent.includes('popup.css')) {
                this.errors.push('ملف CSS غير مربوط في popup.html');
            }
            if (!htmlContent.includes('popup.js')) {
                this.errors.push('ملف JavaScript غير مربوط في popup.html');
            }

        } catch (error) {
            this.errors.push(`خطأ في قراءة popup.html: ${error.message}`);
        }
    }

    validateJavaScript() {
        console.log('\n⚡ التحقق من ملفات JavaScript...');
        
        const jsFiles = ['popup.js', 'content.js', 'background.js'];
        
        jsFiles.forEach(file => {
            try {
                const jsContent = fs.readFileSync(file, 'utf8');
                
                // التحقق من الأخطاء النحوية الأساسية
                if (jsContent.includes('console.log') && file === 'background.js') {
                    this.warnings.push(`${file} يحتوي على console.log - قد تحتاج لإزالتها في الإنتاج`);
                }

                // التحقق من استخدام Chrome APIs
                if (file === 'content.js' && !jsContent.includes('chrome.runtime.onMessage')) {
                    this.warnings.push(`${file} قد لا يستمع للرسائل بشكل صحيح`);
                }

                if (file === 'background.js' && !jsContent.includes('chrome.downloads')) {
                    this.errors.push(`${file} لا يستخدم Downloads API`);
                }

                console.log(`✅ ${file} - تم التحقق`);

            } catch (error) {
                this.errors.push(`خطأ في قراءة ${file}: ${error.message}`);
            }
        });
    }

    validateIcons() {
        console.log('\n🎨 التحقق من الأيقونات...');
        
        const iconSizes = [16, 32, 48, 128];
        
        iconSizes.forEach(size => {
            const iconPath = `icons/icon${size}.png`;
            try {
                const stats = fs.statSync(iconPath);
                if (stats.size === 0) {
                    this.errors.push(`الأيقونة فارغة: ${iconPath}`);
                } else if (stats.size > 50000) {
                    this.warnings.push(`الأيقونة كبيرة الحجم: ${iconPath} (${stats.size} bytes)`);
                } else {
                    console.log(`✅ ${iconPath} - ${stats.size} bytes`);
                }
            } catch (error) {
                this.errors.push(`خطأ في قراءة الأيقونة ${iconPath}: ${error.message}`);
            }
        });
    }

    printResults() {
        console.log('\n' + '='.repeat(50));
        console.log('📊 نتائج التحقق');
        console.log('='.repeat(50));

        if (this.errors.length === 0) {
            console.log('🎉 تهانينا! الإضافة جاهزة للاستخدام');
        } else {
            console.log(`❌ تم العثور على ${this.errors.length} خطأ:`);
            this.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }

        if (this.warnings.length > 0) {
            console.log(`\n⚠️  تحذيرات (${this.warnings.length}):`);
            this.warnings.forEach((warning, index) => {
                console.log(`   ${index + 1}. ${warning}`);
            });
        }

        console.log('\n📋 ملخص:');
        console.log(`   ✅ الملفات الموجودة: ${this.requiredFiles.filter(f => fs.existsSync(f)).length}/${this.requiredFiles.length}`);
        console.log(`   ❌ الأخطاء: ${this.errors.length}`);
        console.log(`   ⚠️  التحذيرات: ${this.warnings.length}`);
        
        if (this.errors.length === 0) {
            console.log('\n🚀 الخطوات التالية:');
            console.log('   1. افتح Chrome واذهب إلى chrome://extensions/');
            console.log('   2. فعل "Developer mode"');
            console.log('   3. انقر على "Load unpacked"');
            console.log('   4. اختر مجلد الإضافة');
            console.log('   5. اختبر الإضافة على test-page.html');
        }
    }
}

// تشغيل التحقق
if (require.main === module) {
    const validator = new ExtensionValidator();
    const isValid = validator.validate();
    process.exit(isValid ? 0 : 1);
}

module.exports = ExtensionValidator;
