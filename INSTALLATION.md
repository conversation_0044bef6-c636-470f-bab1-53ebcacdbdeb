# 🚀 دليل تثبيت إضافة Chrome Images Downloader Pro

## المتطلبات الأساسية

- **Google Chrome** الإصدار 88 أو أحدث
- **نظام التشغيل**: Windows, macOS, أو Linux
- **مساحة تخزين**: 50 MB على الأقل للإضافة والصور المحملة

## خطوات التثبيت

### الطريقة الأولى: التثبيت اليدوي (مُوصى بها)

#### 1. تحضير الملفات
```
✅ تأكد من وجود جميع الملفات التالية:
├── manifest.json
├── popup.html
├── popup.css
├── popup.js
├── content.js
├── background.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── README.md
└── test-page.html
```

#### 2. فتح صفحة الإضافات في Chrome
1. افتح **Google Chrome**
2. اكتب في شريط العنوان: `chrome://extensions/`
3. اضغط **Enter**

#### 3. تفعيل وضع المطور
1. في الزاوية العلوية اليمنى، فعل مفتاح **"Developer mode"**
2. ستظهر أزرار جديدة في الأعلى

#### 4. تحميل الإضافة
1. انقر على زر **"Load unpacked"**
2. اختر المجلد الذي يحتوي على ملفات الإضافة
3. انقر **"Select Folder"**

#### 5. التحقق من التثبيت
- ✅ ستظهر الإضافة في قائمة الإضافات
- ✅ ستظهر أيقونة الإضافة في شريط الأدوات
- ✅ يمكنك النقر على الأيقونة لفتح واجهة التحكم

## اختبار الإضافة

### الاختبار السريع
1. افتح ملف `test-page.html` في Chrome
2. انقر على أيقونة الإضافة
3. اضبط الحد الأدنى للأبعاد (جرب 150x150)
4. انقر على **"بدء التحميل"**
5. راقب الإحصائيات في نافذة الإضافة

### اختبار متقدم
1. اذهب إلى أي موقع يحتوي على صور (مثل Pinterest أو Unsplash)
2. فعل الإضافة وابدأ التحميل
3. قم بالتمرير لأسفل لرؤية الصور الجديدة
4. تحقق من مجلد التحميلات

## إعدادات التحميل

### تغيير مجلد التحميل
1. اذهب إلى إعدادات Chrome: `chrome://settings/`
2. انقر على **"Advanced"** → **"Downloads"**
3. غير مكان التحميل حسب رغبتك
4. الإضافة ستحفظ الصور في مجلد فرعي: `Chrome_Images_Downloader/`

### إعدادات الأمان
- تأكد من تفعيل **"Ask where to save each file before downloading"** إذا كنت تريد التحكم في كل تحميل
- أو ألغ تفعيلها للتحميل التلقائي

## استكشاف الأخطاء

### المشاكل الشائعة

#### ❌ الإضافة لا تظهر في شريط الأدوات
**الحل:**
1. اذهب إلى `chrome://extensions/`
2. تأكد من أن الإضافة **مفعلة** (enabled)
3. انقر على أيقونة الإضافات في شريط الأدوات
4. اختر **"Pin"** بجانب اسم الإضافة

#### ❌ لا يتم تحميل أي صور
**الحلول المحتملة:**
1. **تحقق من الأبعاد**: قلل الحد الأدنى للعرض والارتفاع
2. **تحقق من الصلاحيات**: تأكد من السماح للإضافة بالوصول للموقع
3. **أعد تحميل الصفحة**: اضغط F5 وجرب مرة أخرى
4. **تحقق من إعدادات التحميل**: تأكد من أن Chrome يسمح بالتحميلات

#### ❌ الإضافة تتوقف عن العمل
**الحلول:**
1. **أعد تحميل الإضافة**: اذهب إلى `chrome://extensions/` وانقر على زر التحديث
2. **أعد تشغيل Chrome**: أغلق المتصفح وافتحه مرة أخرى
3. **تحقق من وحدة التحكم**: اضغط F12 وابحث عن أخطاء في Console

#### ❌ تحميل صور غير مرغوب فيها
**الحلول:**
1. **زد الحد الأدنى للأبعاد**: جرب 300x300 أو أكثر
2. **فعل "تجاهل الصور المكررة"**
3. **استخدم الإضافة على مواقع موثوقة فقط**

### رسائل الخطأ الشائعة

| رسالة الخطأ | السبب | الحل |
|-------------|--------|------|
| "Extension context invalidated" | تم إعادة تحميل الإضافة | أعد تحميل الصفحة |
| "Cannot access chrome-extension://" | مشكلة في الصلاحيات | أعد تثبيت الإضافة |
| "Download failed" | مشكلة في الشبكة أو الملف | تحقق من الاتصال |

## الأمان والخصوصية

### ما تفعله الإضافة:
- ✅ تقرأ الصور من الصفحات المفتوحة
- ✅ تحفظ الصور في مجلد التحميلات المحلي
- ✅ تحفظ الإعدادات محلياً في Chrome

### ما لا تفعله الإضافة:
- ❌ لا ترسل أي بيانات لخوادم خارجية
- ❌ لا تجمع معلومات شخصية
- ❌ لا تتتبع نشاطك على الإنترنت
- ❌ لا تعدل محتوى الصفحات

## إلغاء التثبيت

إذا كنت تريد إزالة الإضافة:

1. اذهب إلى `chrome://extensions/`
2. ابحث عن **"Chrome Images Downloader Pro"**
3. انقر على **"Remove"**
4. أكد الحذف

**ملاحظة:** الصور المحملة ستبقى في مجلد التحميلات ولن تُحذف.

## الدعم والمساعدة

### للحصول على المساعدة:
1. راجع ملف `README.md` للمعلومات التفصيلية
2. جرب صفحة الاختبار `test-page.html`
3. تحقق من وحدة التحكم في Chrome (F12)

### للإبلاغ عن مشاكل:
- صف المشكلة بالتفصيل
- اذكر إصدار Chrome المستخدم
- أرفق لقطة شاشة إن أمكن

---

**🎉 تهانينا! الآن يمكنك الاستمتاع بتحميل الصور تلقائياً من أي موقع ويب!**
