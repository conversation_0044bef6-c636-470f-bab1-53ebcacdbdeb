<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        .icon-size {
            text-align: center;
        }
        .icon-size img {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a67d8;
        }
        .download-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chrome Images Downloader - Icon Generator</h1>
        
        <div class="icon-preview">
            <div class="icon-size">
                <h3>16x16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button onclick="downloadIcon(16)">Download 16x16</button>
            </div>
            
            <div class="icon-size">
                <h3>32x32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <br>
                <button onclick="downloadIcon(32)">Download 32x32</button>
            </div>
            
            <div class="icon-size">
                <h3>48x48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button onclick="downloadIcon(48)">Download 48x48</button>
            </div>
            
            <div class="icon-size">
                <h3>128x128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button onclick="downloadIcon(128)">Download 128x128</button>
            </div>
        </div>
        
        <div class="download-section">
            <button onclick="downloadAllIcons()">Download All Icons</button>
        </div>
    </div>

    <script>
        const svgContent = `<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <circle cx="64" cy="64" r="60" fill="url(#bgGradient)" stroke="#5a67d8" stroke-width="2"/>
  
  <g fill="url(#iconGradient)">
    <rect x="58" y="25" width="12" height="35" rx="2"/>
    <polygon points="64,70 45,50 55,50 64,59 73,50 83,50"/>
    <rect x="35" y="75" width="58" height="8" rx="4" fill="url(#iconGradient)"/>
    <rect x="35" y="85" width="58" height="12" rx="6" fill="url(#iconGradient)" opacity="0.8"/>
  </g>
  
  <g fill="none" stroke="url(#iconGradient)" stroke-width="2" opacity="0.7">
    <rect x="20" y="35" width="20" height="15" rx="2"/>
    <circle cx="25" cy="42" r="2" fill="url(#iconGradient)"/>
    <polygon points="22,47 28,42 32,45 38,40" stroke-width="1.5"/>
    
    <rect x="88" y="45" width="18" height="13" rx="2"/>
    <circle cx="92" cy="50" r="1.5" fill="url(#iconGradient)"/>
    <polygon points="90,55 94,51 97,53 104,48" stroke-width="1.5"/>
  </g>
  
  <g fill="#ffffff" opacity="0.8">
    <circle cx="25" cy="25" r="2"/>
    <circle cx="100" cy="30" r="1.5"/>
    <circle cx="105" cy="70" r="2"/>
    <circle cx="20" cy="90" r="1.5"/>
  </g>
</svg>`;

        function generateIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadAllIcons() {
            [16, 32, 48, 128].forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }

        // Generate all icons on page load
        window.onload = function() {
            [16, 32, 48, 128].forEach(size => {
                setTimeout(() => generateIcon(size), 100);
            });
        };
    </script>
</body>
</html>
